#!/usr/bin/env python3
"""
Data Structure Migration Script
Organizes the AI Coach data into a cleaner, more maintainable structure.
"""

import os
import shutil
import json
from pathlib import Path

def create_new_data_structure():
    """Create the new organized data structure."""
    
    # Create main data directory and subdirectories
    data_dirs = [
        "data",
        "data/curriculum",
        "data/techniques", 
        "data/techniques/enablers",
        "data/techniques/retrieval",
        "data/techniques/encoding",
        "data/coaching_brain",
        "data/assessments",
        "data/user_profiles",
        "data/app_guidance"
    ]
    
    for dir_path in data_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"✓ Created directory: {dir_path}")

def migrate_curriculum_data():
    """Migrate curriculum-related data."""
    
    # Copy and enhance curriculum files
    source_files = [
        ("knowledge_data/curriculum.json", "data/curriculum/curriculum.json"),
        ("knowledge_data/course_modules.json", "data/curriculum/course_modules.json"),
        ("Coach_Brain/curriculum_guidance.json", "data/curriculum/curriculum_guidance.json")
    ]
    
    for source, dest in source_files:
        if os.path.exists(source):
            shutil.copy2(source, dest)
            print(f"✓ Migrated: {source} → {dest}")
        else:
            print(f"⚠ Source not found: {source}")

def migrate_technique_data():
    """Migrate technique-specific data."""
    
    technique_migrations = [
        ("knowledge_data/enablers.json", "data/techniques/enablers/enablers.json"),
        ("knowledge_data/retrieval.json", "data/techniques/retrieval/retrieval.json"), 
        ("knowledge_data/encoding.json", "data/techniques/encoding/encoding.json"),
        ("knowledge_data/interleaving.json", "data/techniques/retrieval/interleaving.json")
    ]
    
    for source, dest in technique_migrations:
        if os.path.exists(source):
            shutil.copy2(source, dest)
            print(f"✓ Migrated: {source} → {dest}")
        else:
            print(f"⚠ Source not found: {source}")

def migrate_coaching_brain():
    """Migrate coaching brain data."""
    
    brain_migrations = [
        ("Coach_Brain/coach_brain.json", "data/coaching_brain/strategic_brain.json"),
        ("Coach_Brain/coach_brain_test.md", "data/coaching_brain/coach_brain_test.md"),
        ("Coach_Brain/curriculum2.md", "data/coaching_brain/curriculum2.md")
    ]
    
    for source, dest in brain_migrations:
        if os.path.exists(source):
            shutil.copy2(source, dest)
            print(f"✓ Migrated: {source} → {dest}")
        else:
            print(f"⚠ Source not found: {source}")

def migrate_app_guidance():
    """Migrate app usage and guidance data."""
    
    guidance_migrations = [
        ("knowledge_data/app_usage.json", "data/app_guidance/app_usage.json"),
        ("knowledge_data/roadmap.json", "data/app_guidance/roadmap.json")
    ]
    
    for source, dest in guidance_migrations:
        if os.path.exists(source):
            shutil.copy2(source, dest)
            print(f"✓ Migrated: {source} → {dest}")
        else:
            print(f"⚠ Source not found: {source}")

def migrate_module_data():
    """Migrate module-specific data."""
    
    module_migrations = [
        ("knowledge_data/Module-0_Rapid-Start.json", "data/curriculum/Module-0_Rapid-Start.json"),
        ("knowledge_data/Module-1_Fundamentals-1.json", "data/curriculum/Module-1_Fundamentals-1.json"),
        ("knowledge_data/Module-2_Fundamentals-2.json", "data/curriculum/Module-2_Fundamentals-2.json"),
        ("knowledge_data/Module-3_Briefing.json", "data/curriculum/Module-3_Briefing.json"),
        ("knowledge_data/Module-4_Technique-Training.json", "data/curriculum/Module-4_Technique-Training.json")
    ]
    
    for source, dest in module_migrations:
        if os.path.exists(source):
            shutil.copy2(source, dest)
            print(f"✓ Migrated: {source} → {dest}")
        else:
            print(f"⚠ Source not found: {source}")

def create_academic_profiles():
    """Create academic specialization profiles."""
    
    academic_profiles = {
        "computational_linguistics": {
            "profile_id": "comp_ling_001",
            "profile_name": "Computational Linguistics Specialization",
            "description": "Specialized coaching for computational linguistics students combining programming, algorithms, and linguistic theory.",
            "keywords": ["programming", "algorithm", "python", "nlp", "machine learning", "syntax", "parsing", "computational", "linguistics"],
            "common_challenges": [
                "Balancing theoretical linguistics with practical programming",
                "Managing multiple programming languages and frameworks",
                "Understanding complex algorithms and data structures",
                "Connecting linguistic theory to computational implementation"
            ],
            "recommended_study_patterns": {
                "enablers": {
                    "techniques": ["Strategic Scheduling", "Task Breakdown", "Environment Design"],
                    "specific_guidance": [
                        "Schedule separate blocks for theory vs. coding practice",
                        "Use timeboxing for algorithm implementation sessions",
                        "Create distraction-free environment for complex debugging"
                    ]
                },
                "retrieval": {
                    "techniques": ["Spaced Repetition", "Active Recall", "Practice Testing"],
                    "specific_guidance": [
                        "Use spaced repetition for syntax rules across languages",
                        "Practice active recall for algorithm complexity analysis",
                        "Create coding challenges that test theoretical understanding"
                    ]
                },
                "encoding": {
                    "techniques": ["Concept Mapping", "Feynman Technique", "Analogical Reasoning"],
                    "specific_guidance": [
                        "Map connections between linguistic concepts and algorithms",
                        "Explain complex algorithms in simple terms",
                        "Use analogies to connect abstract CS concepts to linguistic phenomena"
                    ]
                }
            }
        },
        "phonetics_linguistics": {
            "profile_id": "phon_ling_001", 
            "profile_name": "Phonetics & Linguistics Specialization",
            "description": "Specialized coaching for phonetics and linguistics students focusing on auditory skills, transcription, and cross-linguistic analysis.",
            "keywords": ["phonetics", "phonology", "ipa", "transcription", "acoustic", "articulatory", "spanish", "romance", "linguistics"],
            "common_challenges": [
                "Developing accurate auditory discrimination",
                "Mastering IPA transcription under time pressure",
                "Managing cross-linguistic comparison complexity",
                "Analyzing acoustic data and spectrograms"
            ],
            "recommended_study_patterns": {
                "enablers": {
                    "techniques": ["Strategic Scheduling", "Environment Optimization", "Energy Management"],
                    "specific_guidance": [
                        "Schedule daily listening practice at optimal energy times",
                        "Create quiet, acoustically controlled study environment",
                        "Plan transcription practice when auditory attention is peak"
                    ]
                },
                "retrieval": {
                    "techniques": ["Spaced Repetition", "Active Recall", "Interleaving"],
                    "specific_guidance": [
                        "Use spaced repetition for IPA symbol mastery",
                        "Practice active recall for phonological rules",
                        "Interleave different language families in practice sessions"
                    ]
                },
                "encoding": {
                    "techniques": ["Visual Mapping", "Comparative Analysis", "Pattern Recognition"],
                    "specific_guidance": [
                        "Create visual maps of sound systems and relationships",
                        "Use systematic comparison frameworks for cross-linguistic analysis",
                        "Develop pattern recognition skills for phonological processes"
                    ]
                }
            }
        }
    }
    
    # Save academic profiles
    with open("data/user_profiles/academic_specializations.json", 'w', encoding='utf-8') as f:
        json.dump(academic_profiles, f, indent=2, ensure_ascii=False)
    
    print("✓ Created academic specialization profiles")

def create_migration_summary():
    """Create a summary of the migration."""
    
    summary = {
        "migration_date": "2024-12-19",
        "migration_purpose": "Organize AI Coach data structure for better maintainability and scalability",
        "changes": {
            "new_structure": {
                "data/curriculum/": "Course curriculum, modules, and progression logic",
                "data/techniques/": "Individual learning techniques organized by pillar",
                "data/coaching_brain/": "Strategic coaching intelligence and scenario handlers",
                "data/assessments/": "Assessment frameworks and criteria",
                "data/user_profiles/": "Academic specializations and user archetypes",
                "data/app_guidance/": "Application usage instructions and help"
            },
            "deprecated_folders": [
                "Coach_Brain/ (migrated to data/coaching_brain/)",
                "knowledge_data/ (distributed across data/ subfolders)"
            ],
            "new_features": [
                "Academic specialization profiles for computational linguistics and phonetics",
                "Enhanced curriculum with 3-pillar enforcement",
                "Active coaching brain with proactive triggers"
            ]
        },
        "backward_compatibility": "Original files preserved, new structure is additive",
        "next_steps": [
            "Update coach.py to use new data structure",
            "Test active_coach.py with new organization", 
            "Gradually deprecate old folder structure"
        ]
    }
    
    with open("data/migration_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print("✓ Created migration summary")

def main():
    """Run the complete data migration."""
    
    print("🔄 Starting AI Coach Data Structure Migration...")
    print("=" * 60)
    
    # Create new structure
    create_new_data_structure()
    print()
    
    # Migrate data
    print("📁 Migrating curriculum data...")
    migrate_curriculum_data()
    print()
    
    print("🛠 Migrating technique data...")
    migrate_technique_data()
    print()
    
    print("🧠 Migrating coaching brain...")
    migrate_coaching_brain()
    print()
    
    print("📖 Migrating app guidance...")
    migrate_app_guidance()
    print()
    
    print("📚 Migrating module data...")
    migrate_module_data()
    print()
    
    # Create new content
    print("👨‍🎓 Creating academic profiles...")
    create_academic_profiles()
    print()
    
    print("📋 Creating migration summary...")
    create_migration_summary()
    print()
    
    print("=" * 60)
    print("✅ Migration completed successfully!")
    print()
    print("📂 New data structure created in 'data/' folder")
    print("🔄 Original files preserved for backward compatibility")
    print("🚀 Ready to use active_coach.py with enhanced features")
    print()
    print("Next steps:")
    print("1. Test the new active_coach.py script")
    print("2. Verify all data migrated correctly")
    print("3. Update any custom scripts to use new structure")

if __name__ == "__main__":
    main()
