{"migration_date": "2024-12-19", "migration_purpose": "Organize AI Coach data structure for better maintainability and scalability", "changes": {"new_structure": {"data/curriculum/": "Course curriculum, modules, and progression logic", "data/techniques/": "Individual learning techniques organized by pillar", "data/coaching_brain/": "Strategic coaching intelligence and scenario handlers", "data/assessments/": "Assessment frameworks and criteria", "data/user_profiles/": "Academic specializations and user archetypes", "data/app_guidance/": "Application usage instructions and help"}, "deprecated_folders": ["Coach_Brain/ (migrated to data/coaching_brain/)", "knowledge_data/ (distributed across data/ subfolders)"], "new_features": ["Academic specialization profiles for computational linguistics and phonetics", "Enhanced curriculum with 3-pillar enforcement", "Active coaching brain with proactive triggers"]}, "backward_compatibility": "Original files preserved, new structure is additive", "next_steps": ["Update coach.py to use new data structure", "Test active_coach.py with new organization", "Gradually deprecate old folder structure"]}