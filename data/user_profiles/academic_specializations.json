{"computational_linguistics": {"profile_id": "comp_ling_001", "profile_name": "Computational Linguistics Specialization", "description": "Specialized coaching for computational linguistics students combining programming, algorithms, and linguistic theory. Follows strict iCanStudy progression: Enablers → Retrieval → Encoding.", "keywords": ["programming", "algorithm", "python", "nlp", "machine learning", "syntax", "parsing", "computational", "linguistics"], "common_challenges": ["Balancing theoretical linguistics with practical programming", "Managing multiple programming languages and frameworks", "Understanding complex algorithms and data structures", "Connecting linguistic theory to computational implementation"], "progression_pathway": {"phase_1_enablers_only": {"focus": "FOUNDATION FIRST - Build consistent programming and theory study habits", "max_concurrent_techniques": 1, "primary_technique": "Strategic Scheduling", "specific_guidance": ["Establish separate, scheduled blocks for theory vs. coding practice", "Create distraction-free development environment", "Master procrastination solutions for complex debugging sessions", "DO NOT advance to Retrieval until scheduling is consistent"], "mastery_indicators": ["Consistent daily coding practice schedule for 2+ weeks", "Optimized development environment with minimal distractions", "Can break complex algorithms into manageable steps", "Procrastination episodes significantly reduced"], "advancement_criteria": "Only advance to Retrieval phase after Enablers are stabilized"}, "phase_2_retrieval_only": {"focus": "SAFETY NET - Build reliable system to catch programming and theory gaps", "max_concurrent_techniques": 1, "primary_technique": "Retrieved Execution", "specific_guidance": ["Practice coding algorithms from memory (no IDE assistance)", "Use active recall for algorithm complexity analysis", "Create coding challenges that test theoretical understanding", "DO NOT advance to Encoding until retrieval system is solid"], "mastery_indicators": ["Can code basic algorithms from memory without references", "Consistently identifies knowledge gaps through self-testing", "Strong recall of syntax across multiple programming languages", "Confident in debugging skills under pressure"], "advancement_criteria": "Only advance to Encoding after Retrieval safety net is established"}, "phase_3_encoding_only": {"focus": "DEEP UNDERSTANDING - Master computational linguistics concepts", "max_concurrent_techniques": 1, "primary_technique": "Concept Mapping", "specific_guidance": ["Map connections between linguistic concepts and algorithms", "Explain complex algorithms in simple terms (<PERSON><PERSON><PERSON> style)", "ONLY focus here after Enablers and Retrieval are mastered"], "mastery_indicators": ["Can explain complex CS concepts simply to others", "Creates interconnected knowledge structures", "Demonstrates expert-level understanding of computational linguistics"]}}, "coaching_warnings": ["NEVER suggest multiple techniques simultaneously", "NEVER skip Enablers phase - it's the foundation", "NEVER advance to Encoding without solid Retrieval system", "Always assess current phase before suggesting new techniques"]}, "phonetics_linguistics": {"profile_id": "phon_ling_001", "profile_name": "Phonetics & Linguistics Specialization", "description": "Specialized coaching for phonetics and linguistics students focusing on auditory skills, transcription, and cross-linguistic analysis. Follows strict iCanStudy progression: Enablers → Retrieval → Encoding.", "keywords": ["phonetics", "phonology", "ipa", "transcription", "acoustic", "articulatory", "spanish", "romance", "linguistics"], "common_challenges": ["Developing accurate auditory discrimination", "Mastering IPA transcription under time pressure", "Managing cross-linguistic comparison complexity", "Analyzing acoustic data and spectrograms"], "progression_pathway": {"phase_1_enablers_only": {"focus": "FOUNDATION FIRST - Build consistent auditory practice habits", "max_concurrent_techniques": 1, "primary_technique": "Strategic Scheduling", "specific_guidance": ["Schedule daily listening practice at optimal energy times", "Create quiet, acoustically controlled study environment", "Plan transcription practice when auditory attention is peak", "DO NOT advance to Retrieval until scheduling is consistent"], "mastery_indicators": ["Consistent daily listening practice schedule for 2+ weeks", "Optimized acoustic environment for phonetic work", "Can maintain focus during long transcription sessions", "Procrastination on auditory work significantly reduced"], "advancement_criteria": "Only advance to Retrieval phase after Enablers are stabilized"}, "phase_2_retrieval_only": {"focus": "SAFETY NET - Build reliable system to catch phonetic and linguistic gaps", "max_concurrent_techniques": 1, "primary_technique": "Active Recall", "specific_guidance": ["Practice IPA transcription from memory without references", "Use active recall for phonological rules and processes", "Test yourself on sound discrimination without visual cues", "DO NOT advance to Encoding until retrieval system is solid"], "mastery_indicators": ["Can transcribe familiar sounds accurately from memory", "Consistently identifies gaps in phonological knowledge", "Strong recall of IPA symbols and their articulatory descriptions", "Confident in auditory discrimination under time pressure"], "advancement_criteria": "Only advance to Encoding after Retrieval safety net is established"}, "phase_3_encoding_only": {"focus": "DEEP UNDERSTANDING - Master phonetic and linguistic analysis", "max_concurrent_techniques": 1, "primary_technique": "Comparative Analysis", "specific_guidance": ["Create systematic comparison frameworks for cross-linguistic analysis", "Develop pattern recognition skills for phonological processes", "ONLY focus here after Enablers and Retrieval are mastered"], "mastery_indicators": ["Can analyze complex phonological patterns across languages", "Creates sophisticated comparative frameworks", "Demonstrates expert-level understanding of phonetic principles"]}}, "coaching_warnings": ["NEVER suggest multiple techniques simultaneously", "NEVER skip Enablers phase - auditory work requires strong habits", "NEVER advance to Encoding without solid Retrieval system", "Always assess current phase before suggesting new techniques"]}}