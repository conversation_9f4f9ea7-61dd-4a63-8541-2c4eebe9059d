#!/usr/bin/env python3
"""
Active AI Learning Coach - Enhanced Version
Implements proactive coaching with academic specialization and 3-pillar enforcement.
"""

import os
import sqlite3
import json
import datetime
import time
import threading
import chromadb
import google.generativeai as genai
from dotenv import load_dotenv
import warnings
import re

# Suppress deprecation warnings for cleaner interface
warnings.filterwarnings("ignore", category=DeprecationWarning)

# --- CONFIGURATION AND CONSTANTS ---
load_dotenv()

DB_PATH = "db/user_memory.db"
DATA_DIR = "data"
KNOWLEDGE_BASE_DIR = "knowledge_data"  # Keep for backward compatibility
CHROMA_DB_PATH = "db/knowledge_vectordb"
KNOWLEDGE_BASE_COLLECTION_NAME = "ics_knowledge_base"


class ActiveCoachingEngine:
    """Core engine for active coaching behaviors."""

    def __init__(self, db_path, data_dir):
        self.db_path = db_path
        self.data_dir = data_dir
        self.active_techniques = {"enablers": None,
                                  "retrieval": None, "encoding": None}
        self.technique_count = 0
        self.max_techniques = 3
        self.user_academic_profile = None
        self.last_check_time = datetime.datetime.now()
        self.check_interval = 300  # 5 minutes

    def load_active_coaching_brain(self):
        """Load the active coaching intelligence."""
        brain_path = os.path.join(
            self.data_dir, "coaching_brain", "active_coach_brain.json")
        try:
            with open(brain_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: Active coaching brain not found at {brain_path}")
            return []

    def detect_academic_context(self, user_input):
        """Detect academic specialization from user input."""
        user_input_lower = user_input.lower()

        # Computational Linguistics keywords
        comp_ling_keywords = ["programming", "algorithm", "python", "nlp", "machine learning",
                              "syntax", "parsing", "computational", "linguistics", "neural networks"]

        # Phonetics/Linguistics keywords
        phon_ling_keywords = ["phonetics", "phonology", "ipa", "transcription", "acoustic",
                              "articulatory", "spanish", "romance", "linguistics", "phonemes"]

        comp_ling_score = sum(
            1 for keyword in comp_ling_keywords if keyword in user_input_lower)
        phon_ling_score = sum(
            1 for keyword in phon_ling_keywords if keyword in user_input_lower)

        if comp_ling_score >= 2:
            return "computational_linguistics"
        elif phon_ling_score >= 2:
            return "phonetics_linguistics"

        return None

    def enforce_three_pillar_system(self, requested_technique, technique_pillar):
        """Enforce the 3-pillar system (max 1 technique per pillar)."""
        if self.active_techniques[technique_pillar] is not None:
            return {
                "allowed": False,
                "message": f"You're already working on {self.active_techniques[technique_pillar]} in the {technique_pillar} pillar. Let's master this first before adding {requested_technique}.",
                "suggestion": f"Focus on strengthening {self.active_techniques[technique_pillar]} until it becomes automatic."
            }

        if self.technique_count >= self.max_techniques:
            active_list = [
                tech for tech in self.active_techniques.values() if tech is not None]
            return {
                "allowed": False,
                "message": f"You're already at the maximum of 3 techniques: {', '.join(active_list)}. Which one would you like to replace with {requested_technique}?",
                "suggestion": "Master your current techniques before adding new ones."
            }

        return {"allowed": True}

    def add_technique(self, technique_name, pillar):
        """Add a technique to the active set."""
        if self.active_techniques[pillar] is None and self.technique_count < self.max_techniques:
            self.active_techniques[pillar] = technique_name
            self.technique_count += 1
            return True
        return False

    def check_proactive_triggers(self):
        """Check for proactive coaching triggers."""
        now = datetime.datetime.now()
        triggers = []

        # Time-based triggers
        if 6 <= now.hour < 10:
            triggers.append({
                "type": "morning_start",
                "message": "Good morning! Ready to tackle your learning goals today? What's your main focus for this session?",
                "priority": "medium"
            })
        elif 23 <= now.hour or now.hour <= 5:
            triggers.append({
                "type": "late_night_study",
                "message": "⚠️ It's quite late! Your brain needs sleep for memory consolidation. Consider wrapping up and getting rest.",
                "priority": "high"
            })

        # Progress-based triggers
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check for practice gaps
        cursor.execute("""
            SELECT MAX(log_date) FROM Practice_Log WHERE user_id = 1
        """)
        last_practice = cursor.fetchone()[0]
        if last_practice:
            last_practice_date = datetime.datetime.strptime(
                last_practice, '%Y-%m-%d %H:%M:%S')
            days_since_practice = (now - last_practice_date).days
            if days_since_practice >= 3:
                triggers.append({
                    "type": "no_practice_3_days",
                    "message": f"I noticed it's been {days_since_practice} days since your last practice session. What's been happening? Let's get back on track together.",
                    "priority": "high"
                })

        conn.close()
        return triggers


class AcademicContextManager:
    """Manages academic context and specialized guidance."""

    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.academic_profiles = self.load_academic_profiles()

    def load_academic_profiles(self):
        """Load academic specialization profiles."""
        # For now, return hardcoded profiles - can be moved to JSON later
        return {
            "computational_linguistics": {
                "keywords": ["programming", "algorithm", "python", "nlp", "machine learning", "syntax", "parsing"],
                "specialized_guidance": [
                    "Schedule dedicated coding practice separate from theory study",
                    "Use active recall for algorithm complexity analysis",
                    "Apply concept mapping to connect linguistic theory with computational implementation"
                ],
                "recommended_techniques": {
                    "enablers": ["Strategic Scheduling", "Task Breakdown", "Environment Design"],
                    "retrieval": ["Spaced Repetition for syntax", "Active Recall for algorithms"],
                    "encoding": ["Concept Mapping for architectures", "Feynman for complex theories"]
                }
            },
            "phonetics_linguistics": {
                "keywords": ["phonetics", "phonology", "ipa", "transcription", "acoustic", "articulatory", "spanish"],
                "specialized_guidance": [
                    "Schedule regular auditory discrimination practice",
                    "Use spaced repetition for IPA symbol mastery",
                    "Apply comparative analysis frameworks for cross-linguistic study"
                ],
                "recommended_techniques": {
                    "enablers": ["Scheduled listening practice", "Environment optimization for audio"],
                    "retrieval": ["Spaced repetition for IPA", "Active recall for sound patterns"],
                    "encoding": ["Visual mapping of sound systems", "Comparative analysis frameworks"]
                }
            }
        }

    def get_specialized_guidance(self, academic_field, user_input):
        """Get specialized guidance based on academic field."""
        if academic_field not in self.academic_profiles:
            return None

        profile = self.academic_profiles[academic_field]

        # Check if user input relates to this field
        user_input_lower = user_input.lower()
        keyword_matches = sum(
            1 for keyword in profile["keywords"] if keyword in user_input_lower)

        if keyword_matches >= 1:
            return {
                "field": academic_field,
                "guidance": profile["specialized_guidance"],
                "recommended_techniques": profile["recommended_techniques"]
            }

        return None


class CourseProgressionManager:
    """Manages intelligent course progression and technique recommendations."""

    def __init__(self, db_path, data_dir):
        self.db_path = db_path
        self.data_dir = data_dir
        self.curriculum = self.load_curriculum()

    def load_curriculum(self):
        """Load the enhanced curriculum."""
        curriculum_path = os.path.join(
            self.data_dir, "curriculum", "enhanced_curriculum.json")
        try:
            with open(curriculum_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data[0] if data else {}
        except FileNotFoundError:
            print(
                f"Warning: Enhanced curriculum not found at {curriculum_path}")
            return {}

    def assess_readiness_for_technique(self, requested_technique, user_id=1):
        """Assess if user is ready for a requested technique."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get user's current skills and confidence levels
        cursor.execute("""
            SELECT concept_id, competence_level, confidence_score 
            FROM Skills WHERE user_id = ?
        """, (user_id,))

        skills = cursor.fetchall()
        conn.close()

        # Simple readiness logic - can be enhanced
        foundation_skills = ["schedule_system",
                             "time_management", "environment_design"]
        has_foundation = any(
            skill[0] in foundation_skills and skill[2] >= 4.0 for skill in skills)

        if not has_foundation and requested_technique not in foundation_skills:
            return {
                "ready": False,
                "reason": "foundation_missing",
                "message": f"Before we explore {requested_technique}, let's ensure your foundation is solid. How confident do you feel with your current schedule and time management system?"
            }

        return {"ready": True}

    def get_next_recommended_technique(self, current_pillar, user_id=1):
        """Get the next recommended technique based on user progress."""
        # This would implement intelligent progression logic
        # For now, return a simple recommendation
        recommendations = {
            "enablers": ["Growth Habit Mapping", "Minimum Viable Goals", "Strategic Scheduling"],
            "retrieval": ["Active Recall", "Spaced Repetition", "Interleaving"],
            "encoding": ["Feynman Technique", "Concept Mapping", "Analogical Reasoning"]
        }

        return recommendations.get(current_pillar, [])


class ActiveAICoach:
    """Main Active AI Coach with proactive capabilities."""

    def __init__(self):
        self.coaching_engine = ActiveCoachingEngine(DB_PATH, DATA_DIR)
        self.academic_manager = AcademicContextManager(DATA_DIR)
        self.progression_manager = CourseProgressionManager(DB_PATH, DATA_DIR)
        self.setup_ai()
        self.setup_database()
        self.setup_knowledge_base()
        self.is_running = False
        self.proactive_thread = None

    def setup_ai(self):
        """Configure the AI model."""
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in .env file")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')

    def setup_database(self):
        """Setup database connection and tables."""
        os.makedirs("db", exist_ok=True)
        conn = sqlite3.connect(DB_PATH)

        # Add active coaching tables
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Active_Techniques (
                technique_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                technique_name TEXT NOT NULL,
                pillar TEXT NOT NULL,
                start_date DATE DEFAULT CURRENT_DATE,
                confidence_score REAL DEFAULT 0.0,
                practice_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'active',
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS Proactive_Interventions (
                intervention_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                trigger_type TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_response TEXT,
                effectiveness_rating INTEGER,
                FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
            )
        ''')

        conn.commit()
        conn.close()

    def setup_knowledge_base(self):
        """Setup ChromaDB knowledge base."""
        os.makedirs(CHROMA_DB_PATH, exist_ok=True)
        self.chroma_client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        self.collection = self.chroma_client.get_or_create_collection(
            name=KNOWLEDGE_BASE_COLLECTION_NAME)

    def start_proactive_monitoring(self):
        """Start the proactive coaching monitoring thread."""
        self.is_running = True
        self.proactive_thread = threading.Thread(
            target=self._proactive_monitoring_loop)
        self.proactive_thread.daemon = True
        self.proactive_thread.start()
        print("🤖 Proactive coaching monitoring started...")

    def stop_proactive_monitoring(self):
        """Stop the proactive coaching monitoring."""
        self.is_running = False
        if self.proactive_thread:
            self.proactive_thread.join()
        print("🤖 Proactive coaching monitoring stopped.")

    def _proactive_monitoring_loop(self):
        """Background loop for proactive coaching checks."""
        while self.is_running:
            try:
                triggers = self.coaching_engine.check_proactive_triggers()
                for trigger in triggers:
                    if trigger["priority"] == "high":
                        self._send_proactive_message(trigger)

                time.sleep(self.coaching_engine.check_interval)
            except Exception as e:
                print(f"Error in proactive monitoring: {e}")
                time.sleep(60)  # Wait a minute before retrying

    def _send_proactive_message(self, trigger):
        """Send a proactive coaching message."""
        print(f"\n🤖 Coach (Proactive): {trigger['message']}")

        # Log the intervention
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO Proactive_Interventions (user_id, trigger_type, message)
            VALUES (?, ?, ?)
        """, (1, trigger["type"], trigger["message"]))
        conn.commit()
        conn.close()

    def respond(self, user_input):
        """Generate intelligent coaching response."""
        # Detect academic context
        academic_context = self.coaching_engine.detect_academic_context(
            user_input)
        if academic_context:
            self.coaching_engine.user_academic_profile = academic_context

        # Get specialized guidance if applicable
        specialized_guidance = None
        if academic_context:
            specialized_guidance = self.academic_manager.get_specialized_guidance(
                academic_context, user_input)

        # Check for technique requests and enforce 3-pillar system
        technique_enforcement = self._check_technique_request(user_input)

        # Build comprehensive prompt
        prompt = self._build_coaching_prompt(
            user_input, academic_context, specialized_guidance, technique_enforcement)

        # Generate response
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"I'm having trouble processing that right now. Could you rephrase? (Error: {e})"

    def _check_technique_request(self, user_input):
        """Check if user is requesting a new technique and enforce limits."""
        user_input_lower = user_input.lower()

        # Simple technique detection - can be enhanced
        technique_keywords = {
            "enablers": ["schedule", "planning", "time management", "procrastination", "mvg"],
            "retrieval": ["spaced repetition", "active recall", "flashcards", "practice testing"],
            "encoding": ["feynman", "concept mapping", "mind mapping", "analogies"]
        }

        for pillar, keywords in technique_keywords.items():
            for keyword in keywords:
                if keyword in user_input_lower:
                    # Check if this would violate the 3-pillar system
                    enforcement = self.coaching_engine.enforce_three_pillar_system(
                        keyword, pillar)
                    if not enforcement["allowed"]:
                        return enforcement
                    break

        return {"allowed": True}

    def _build_coaching_prompt(self, user_input, academic_context, specialized_guidance, technique_enforcement):
        """Build comprehensive coaching prompt."""
        now = datetime.datetime.now()

        prompt = f"""You are an intelligent, proactive AI Learning Coach using the iCanStudy methodology.

CURRENT SITUATION:
Time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}
User says: "{user_input}"

ACTIVE COACHING CONTEXT:
- Current active techniques: {self.coaching_engine.active_techniques}
- Technique count: {self.coaching_engine.technique_count}/3 (max)
- Academic profile: {academic_context or 'General'}

ACADEMIC SPECIALIZATION:
{f"Field: {academic_context}" if academic_context else "No specific field detected"}
{f"Specialized guidance: {specialized_guidance['guidance']}" if specialized_guidance else ""}

TECHNIQUE ENFORCEMENT:
{technique_enforcement.get('message', 'No technique limits triggered')}

COACHING INSTRUCTIONS:
1. Be proactive and intelligent, not just reactive
2. Enforce the 3-pillar system (max 1 technique per pillar, 3 total)
3. Provide academic-specific guidance when relevant
4. Guide users through proper course progression
5. Be encouraging but firm about foundations
6. Keep responses concise but actionable
7. Always end with a question to maintain engagement

Your response:"""

        return prompt


def main():
    """Main application loop with active coaching."""
    print("🎯 Active AI Learning Coach - Enhanced Version")
    print("💬 I'm your proactive learning partner - I'll check in on you and guide your progress")
    print("🧠 Specialized for Computational Linguistics & Phonetics/Linguistics")
    print("⚡ 3-Pillar System: Max 3 techniques (1 Enabler + 1 Retrieval + 1 Encoding)")
    print("🚪 Type 'quit' to exit\n")

    try:
        coach = ActiveAICoach()

        # Start proactive monitoring
        coach.start_proactive_monitoring()

        # Time-based greeting
        now = datetime.datetime.now()
        if now.hour >= 23 or now.hour <= 5:
            print(
                "🤖 Coach: ⚠️ It's very late! You should be sleeping for better learning tomorrow.")
        elif 6 <= now.hour < 12:
            print("🤖 Coach: Good morning! Ready to tackle your learning goals today? What's your main focus for this session?")
        else:
            print("🤖 Coach: Hello! I'm your active AI learning coach. I'll help guide you through the iCanStudy methodology. What would you like to work on?")

        while True:
            try:
                user_input = input("\n👤 You: ").strip()

                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print(
                        "🤖 Coach: Great session! I'll keep monitoring your progress. Keep building those learning habits!")
                    break

                if not user_input:
                    continue

                # Generate coaching response
                response = coach.respond(user_input)
                print(f"\n🤖 Coach: {response}")

            except KeyboardInterrupt:
                print("\n🤖 Coach: Session interrupted. Keep up the great work!")
                break
            except Exception as e:
                print(f"Error: {e}")

    except Exception as e:
        print(f"Failed to initialize coach: {e}")
    finally:
        try:
            coach.stop_proactive_monitoring()
        except:
            pass


if __name__ == "__main__":
    main()
